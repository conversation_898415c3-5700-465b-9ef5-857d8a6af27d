import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from '@shares/base.repository';
import { PaginateModel } from 'mongoose';
import { Holder, HolderDocument } from '../schemas/holder.schema';

@Injectable()
export class HolderRepository extends BaseRepository<Holder> {
  constructor(@InjectModel(Holder.name) model: PaginateModel<HolderDocument>) {
    super(model);
  }

  async findByTokenAndUser(tokenAddress: string, userAddress: string) {
    return this.model.findOne({ tokenAddress, userAddress });
  }

  async findByToken(tokenAddress: string) {
    return this.model.find({ tokenAddress }).sort({ amount: -1 });
  }

  async findByUser(userAddress: string) {
    return this.model.find({ userAddress });
  }

  async getTopHolders(tokenAddress: string, limit: number = 100) {
    return this.model.find({ tokenAddress }).sort({ amount: -1 }).limit(limit);
  }
}
