import { Injectable, NotFoundException } from '@nestjs/common';
import { AggregatePaginateResult } from 'mongoose';
import { EDex, HolderDto, ListCoinDto } from './dtos/coin.dto';
import { CoinRepository } from './repositories/coin.repository';
import { PaginationDto } from '@shares/dtos/pagination.dto';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import {
  StakedCoinDocument,
  StakedSHROCoinDocument,
} from '@modules/coin/schemas/coin.schema';
import { StakingUserService } from '@modules/staking/services';
import { Decimal128 } from 'bson';
import { BigNumber } from 'bignumber.js';
import { EVMClientUtils } from '@shares/utils/evm-provider';
import { toChecksumAddress } from '@shares/utils/evm-utils';

@Injectable()
export class CoinService {
  constructor(
    private readonly coinRepository: CoinRepository,
    private configService: ConfigService,
    private readonly stakingUserService: StakingUserService,
  ) {}

  async getCoinsWithPagination(query: ListCoinDto) {
    const { page, limit, search, sortBy, sortOrder, dex } = query;

    const matchStage: any = {
      tokenAddress: {
        $ne: this.configService.getOrThrow('app.shroTokenAddress'),
      },
    };
    if (search) {
      matchStage.$or = [
        { name: { $regex: search, $options: 'i' } },
        { symbol: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tokenAddress: { $regex: search, $options: 'i' } },
      ];
    }

    if (dex) {
      switch (dex) {
        case EDex.HYPER_SWAP:
          matchStage.bondingDex = EDex.HYPER_SWAP;
          break;
        case EDex.ALL:
          matchStage.bondingDex = {
            $in: [EDex.HYPER_SWAP, null],
          };
          break;
      }

      matchStage.listedAt = { $ne: null };
    }

    const sortStage = { [sortBy]: sortOrder === 'asc' ? 1 : -1, _id: -1 };

    const pipeline: any[] = [{ $match: matchStage }, { $sort: sortStage }];

    const data = await this.coinRepository.aggregatePagination(pipeline, {
      page,
      limit,
    });
    return data;
  }

  async getByTokenAddress(tokenAddress: string) {
    const coin = await this.coinRepository.findOne({ tokenAddress });
    if (!coin) {
      throw new NotFoundException('Coin not found');
    }
    return coin;
  }

  async getBySlug(slug: string) {
    const coin = await this.coinRepository.findOne({ slug });
    if (!coin) {
      throw new NotFoundException('Coin not found');
    }
    return coin;
  }

  async getByTokenAddressOrSlug(identifier: string) {
    let query: any = {
      $or: [{ tokenAddress: identifier }, { slug: identifier }],
    };
    if (identifier.startsWith('moonbags-')) {
      query = {
        $or: [
          { tokenAddress: identifier },
          { slug: identifier.replace('moonbags-', '') },
          { slug: identifier },
        ],
      };
    }
    const coin = await this.coinRepository.findOne(query);

    if (!coin) {
      throw new NotFoundException('Coin not found');
    }
    return coin;
  }

  async getCoinKingOfTheHill() {
    const pipeline: any[] = [
      {
        $match: {
          isKing: true,
        },
      },
      {
        $lookup: {
          from: 'comments',
          localField: 'tokenAddress',
          foreignField: 'coinAddress',
          as: 'comments',
        },
      },
      {
        $addFields: {
          replyCount: { $size: '$comments' },
        },
      },
    ];
    const coin = await this.coinRepository.aggregate(pipeline);
    return coin[0];
  }

  async getMyCoins(creatorAddress: string, query: PaginationDto) {
    const { page, limit } = query;
    return await this.coinRepository.paginate(
      { creatorAddress },
      {
        page,
        limit,
        sort: { createdAt: -1 },
      },
    );
  }

  async getHolders(input: HolderDto) {
    const { tokenAddress, page, limit } = input;
    const coin = await this.coinRepository.findOne({ tokenAddress });

    if (!coin) {
      throw new NotFoundException('Token address not found');
    }
    try {
      return await axios.get(
        `${this.configService.get('raidenx.uri')}/evm/api/v1/tokens/${coin.tokenAddress}/holders`,
        { params: { tokenAddress: coin.tokenAddress, page, limit } },
      );
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }

  async getStakedTokens(
    query: ListCoinDto,
  ): Promise<AggregatePaginateResult<StakedCoinDocument>> {
    const {
      page,
      limit,
      search,
      stakingByAddress,
      holdingByAddress,
      includeByWalletAddress,
      sortBy,
      sortOrder,
    } = query;

    const holdingCoins = [];
    if (holdingByAddress) {
      const startTime = Date.now();
      const coins =
        await EVMClientUtils.getOwnerTokenBalances(holdingByAddress);
      const endTime = Date.now();
      console.log(
        `getOwnerTokenBalances took ${endTime - startTime}ms for address: ${holdingByAddress}`,
      );

      for (const coin of coins) {
        if (new BigNumber(coin.totalBalance).gt(0)) {
          holdingCoins.push(toChecksumAddress(coin.coinType));
        }
      }
    }

    const matchStage: any = {
      tokenAddress: {
        $ne: this.configService.getOrThrow('app.shroTokenAddress'),
      },
      creatorAddress: { $exists: true },
    };

    if (search) {
      matchStage.$or = [
        { name: { $regex: search, $options: 'i' } },
        { symbol: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tokenAddress: { $regex: search, $options: 'i' } },
      ];
    }

    const pipeline: any[] = [{ $match: matchStage }];

    if (holdingCoins.length > 0) {
      pipeline.push({
        $match: {
          tokenAddress: { $in: holdingCoins },
        },
      });
    }

    if (sortBy) {
      const sortStage = { [sortBy]: sortOrder === 'asc' ? 1 : -1, _id: -1 };
      pipeline.push({ $sort: sortStage });
    }

    pipeline.push({
      $lookup: {
        from: 'staking_users',
        localField: 'tokenAddress',
        foreignField: 'stakingCoinAddress',
        as: 'stakingUsers',
      },
    });

    if (stakingByAddress) {
      pipeline.push({
        $match: {
          'stakingUsers.userAddress': stakingByAddress,
          'stakingUsers.stakedAmount': { $gt: Decimal128.fromString('0') },
        },
      });
    }

    if (includeByWalletAddress) {
      pipeline.push({
        $addFields: {
          includeStakedAmount: {
            $sum: {
              $map: {
                input: {
                  $filter: {
                    input: '$stakingUsers',
                    as: 'user',
                    cond: {
                      $eq: ['$$user.userAddress', includeByWalletAddress],
                    },
                  },
                },
                as: 'user',
                in: { $toDecimal: '$$user.stakedAmount' },
              },
            },
          },
          includeRewardAmount: '0', // TODO: add reward amount
        },
      });
    }

    const data = await this.coinRepository.aggregatePagination(pipeline, {
      page,
      limit,
    });

    // const tokenAddresses = data.docs.map((coin) => coin.tokenAddress);
    // const manyVolumeUsd24h =
    //   await this.candleService.getVolumeUsdIn24h(tokenAddresses);

    const docs = [];
    const startTime = Date.now();
    for (let i = 0; i < data.docs.length; i++) {
      // const volumeUsd24h = manyVolumeUsd24h[i];
      const includeStakedAmount = data.docs[i].includeStakedAmount
        ? data.docs[i].includeStakedAmount.toString()
        : '0';
      let includeRewardAmount = '0';

      if (includeByWalletAddress) {
        const stakingByWalletAddress = data.docs[i].stakingUsers.find(
          (staking) => staking.userAddress === includeByWalletAddress,
        );

        if (stakingByWalletAddress) {
          includeRewardAmount =
            await this.stakingUserService.getTotalDistributedAmountForUser(
              stakingByWalletAddress,
            );
        }
      }

      docs.push({
        ...data.docs[i],
        includeStakedAmount,
        includeRewardAmount,
      });
    }
    const endTime = Date.now();
    console.log(
      `Processing ${data.docs.length} coins to get includeStakedAmount and includeRewardAmount took ${endTime - startTime}ms`,
    );

    return {
      ...data,
      docs,
    };
  }

  async getStakedSHROCoin(
    includeByWalletAddress?: string,
  ): Promise<StakedSHROCoinDocument> {
    const pipeline: any[] = [
      {
        $match: {
          tokenAddress: this.configService.getOrThrow('app.shroTokenAddress'),
        },
      },
    ];

    if (includeByWalletAddress) {
      pipeline.push(
        {
          $lookup: {
            from: 'staking_users',
            let: {
              tokenAddress: '$tokenAddress',
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$stakingCoinAddress', '$$tokenAddress'] },
                      { $eq: ['$userAddress', includeByWalletAddress] },
                    ],
                  },
                },
              },
            ],
            as: 'stakingUsers',
          },
        },
        {
          $addFields: {
            includeStakedAmount: {
              $sum: {
                $map: {
                  input: '$stakingUsers',
                  as: 'user',
                  in: { $toDecimal: '$$user.stakedAmount' },
                },
              },
            },
          },
        },
      );
    }

    const data = await this.coinRepository.aggregate(pipeline);
    if (data.length === 0) {
      throw new NotFoundException('No staked SHRO coin found');
    }

    const shroCoin = data[0];

    const totalVolumeUsd24hAllCoin =
      await this.coinRepository.getTotalVolumeUsd24h();

    let includeRewardAmount = '0';
    if (includeByWalletAddress) {
      // only calculate reward amount if includeByWalletAddress is provided
      const stakingByWalletAddress = shroCoin.stakingUsers.find(
        (staking) => staking.userAddress === includeByWalletAddress,
      );

      if (stakingByWalletAddress) {
        includeRewardAmount =
          await this.stakingUserService.getTotalDistributedAmountForUser(
            stakingByWalletAddress,
          );
      }
    }

    return {
      ...shroCoin,
      includeStakedAmount: shroCoin.includeStakedAmount
        ? shroCoin.includeStakedAmount.toString()
        : '0',
      includeRewardAmount,
      totalVolumeUsd24hAllCoin,
    };
  }
}
