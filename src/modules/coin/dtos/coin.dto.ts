import { CoinSocial } from '@modules/coin/schemas/coin.schema';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaginatedResponseDto } from '@shares/dtos/paginated-response.dto';
import { PaginationDto } from '@shares/dtos/pagination.dto';
import { ESortBy, ESortOrder } from '@shares/enums/coin.enum';
import { BigNumber } from 'bignumber.js';
import { Expose, Transform, Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import {
  divideBN,
  minusBN,
  multiplyBN,
  toStringBN,
} from 'src/utils/number.utils';
import {
  IsEvmAddress,
  IsRequiredEvmAddress,
} from '@shares/decorators/evm-address.decorator';
const HYPE_DECIMALS = 18;

export enum EDex {
  ALL = 'All',
  HYPER_SWAP = 'Hyperswap',
}

export class CreateCoinDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  symbol: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  telegram?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  x?: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'Upload image or video',
  })
  media: Express.Multer.File;
}

export class ListCoinDto extends PaginationDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  search?: string;

  @IsEvmAddress({ description: 'Filter coins by staking address' })
  stakingByAddress?: string;

  @IsEvmAddress({ description: 'Filter coins by holding address' })
  holdingByAddress?: string;

  @IsEvmAddress({ description: 'Wallet address to include staked coins' })
  includeByWalletAddress?: string;

  @ApiPropertyOptional({
    enum: ESortBy,
    default: ESortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(ESortBy)
  sortBy?: ESortBy = ESortBy.CREATED_AT;

  @ApiPropertyOptional({
    enum: ESortOrder,
    default: ESortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(ESortOrder)
  sortOrder?: ESortOrder = ESortOrder.DESC;

  @ApiPropertyOptional({
    enum: EDex,
    default: EDex.HYPER_SWAP,
  })
  @IsOptional()
  @IsEnum(EDex)
  dex?: EDex;
}

export class CreateCoinResponseDto {
  @Expose()
  id: string;

  @Expose()
  tokenAddress: string;

  @Expose()
  name: string;

  @Expose()
  description: string;

  @Expose()
  symbol: string;

  @Expose()
  logoUri: string;

  @Expose()
  social: {
    x: string;
    website: string;
    telegram: string;
  };

  @Expose()
  createdAt: string;

  @Expose()
  updatedAt: string;
}

class CoinRewardPoolDto {
  @Expose()
  @ApiProperty({
    type: String,
  })
  address: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  stakedAmount?: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  totalReward?: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  rewardClaimed?: string;
}

class CoinRewardShroPoolDto {
  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  totalReward: string;
}

class CoinFeeRatesDto {
  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  platformFeeWithdrawRate: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  creatorFeeWithdrawRate: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  stakeFeeWithdrawRate: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  platformStakeFeeWithdrawRate: string;
}

class CoinRewardCreatorPoolDto {
  @Expose()
  @ApiProperty({
    type: String,
  })
  address: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  totalReward?: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  rewardClaimed?: string;
}

export class CoinResponseDto {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  @ApiProperty({
    type: String,
  })
  id: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  tokenAddress: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  name: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  symbol: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  description: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  logoUri: string;

  @Expose()
  @ApiProperty({
    type: CoinSocial,
  })
  @Type(() => CoinSocial)
  socials: CoinSocial;

  @Expose()
  @ApiProperty({
    type: String,
  })
  creatorAddress: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  replyCount: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  mcap: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  prevMcap: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  prevRealHypeReserves: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  mcapUsd: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  virtualHypeReserves: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  virtualTokenReserves: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  realHypeReserves: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  realTokenReserves: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  initVirtualHypeReserves: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  initVirtualTokenReserves: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  threshold: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  createdAt: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  updatedAt: string;

  @Expose()
  @ApiProperty({
    type: Number,
  })
  lastTrade: number;

  @Expose()
  @ApiProperty({
    type: Number,
  })
  lastReply: number;

  @Expose()
  @ApiProperty({
    type: Boolean,
  })
  @Type(() => Boolean)
  isKing: boolean;

  @Expose()
  @ApiProperty({
    type: Date,
  })
  @Type(() => Date)
  @Transform(({ value }) => value || null)
  kingAt: Date;

  @Expose()
  @ApiProperty({
    type: Number,
  })
  @Type(() => Number)
  @Transform(({ value, obj }) => {
    if (!!obj?.listedAt) return 100;
    return new BigNumber(value).toNumber() || 0;
  })
  bondingCurve: number;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Transform(({ value }) => value || null)
  listedAt: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Transform(({ value }) => value || null)
  listedPoolId: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Transform(({ value }) => value || null)
  raidenxPairId: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Transform(({ value }) => value || null)
  raidenxCetusPairId: string;

  @Expose()
  @ApiProperty({
    type: CoinRewardPoolDto,
  })
  @Type(() => CoinRewardPoolDto)
  rewardTokenPool?: CoinRewardPoolDto;

  @Expose()
  @ApiProperty({
    type: CoinRewardPoolDto,
  })
  @Type(() => CoinRewardShroPoolDto)
  @Transform(({ obj }) => {
    if (!obj.feeRates) return '0';
    const creatorFeeRate = toStringBN(
      obj?.feeRates?.creatorFeeWithdrawRate || '0',
    );
    const creatorReward = obj?.rewardCreatorPool?.totalReward || '0';

    const totalReward = divideBN(creatorReward, creatorFeeRate);

    const platformStakeFeeRate = toStringBN(
      obj?.feeRates?.platformStakeFeeWithdrawRate || '0',
    );
    const platformStakeReward = multiplyBN(
      totalReward,
      platformStakeFeeRate,
      HYPE_DECIMALS,
      BigNumber.ROUND_DOWN,
    );

    return {
      totalReward: platformStakeReward,
    };
  })
  rewardShroPool?: CoinRewardShroPoolDto;

  @Expose()
  @ApiProperty({
    type: CoinRewardCreatorPoolDto,
  })
  @Type(() => CoinRewardCreatorPoolDto)
  rewardCreatorPool?: CoinRewardCreatorPoolDto;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ value }) => toStringBN(value || '0'))
  volumeUsd24h: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Type(() => String)
  @Transform(({ obj }) => {
    if (!obj?.feeRates) return '0';
    const creatorFeeRate = toStringBN(
      obj?.feeRates?.creatorFeeWithdrawRate || '0',
    );
    const creatorReward = obj?.rewardCreatorPool?.totalReward || '0';

    const totalReward = divideBN(creatorReward, creatorFeeRate);

    const platformFeeRate = toStringBN(
      obj?.feeRates?.platformFeeWithdrawRate || '0',
    );
    const remainingReward = multiplyBN(
      totalReward,
      minusBN(1, platformFeeRate),
      HYPE_DECIMALS,
      BigNumber.ROUND_DOWN,
    );

    return remainingReward;
  })
  totalRewards: string;

  @Expose()
  @ApiProperty({
    type: Number,
  })
  @Type(() => Number)
  @Transform(({ value }) => value || 0)
  totalHolder: number;

  @Expose()
  @ApiProperty({
    type: CoinFeeRatesDto,
  })
  @Type(() => CoinFeeRatesDto)
  feeRates?: CoinFeeRatesDto;

  @Expose()
  @ApiProperty({
    type: Date,
    description: 'Boosted until date',
    default: null,
  })
  @Transform(({ value }) => value || null)
  isBoostedUntil?: Date;

  @Expose()
  @ApiProperty({
    type: Number,
    description: 'Boost factor',
    default: 0,
  })
  @Type(() => Number)
  @Transform(({ value }) => value || 0)
  boostFactor?: number = 0;

  @Expose()
  @ApiProperty({
    type: String,
  })
  @Transform(({ value }) => value || null)
  bondingDex?: string;

  @Expose()
  @ApiProperty({
    type: String,
  })
  slug?: string;
}

export class CoinsResponseDto extends PaginatedResponseDto<CoinResponseDto> {
  @Expose()
  @ApiProperty({ type: [CoinResponseDto] })
  @Type(() => CoinResponseDto)
  docs: CoinResponseDto[];
}

export class HolderDto extends PaginationDto {
  @IsRequiredEvmAddress({
    description: 'Token address to filter holders',
  })
  tokenAddress: string;
}
