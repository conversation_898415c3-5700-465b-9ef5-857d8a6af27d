import { ethers } from 'ethers';

/**
 * Validates if a string is a valid EVM address
 */
export function validateEvmAddress(address: string): boolean {
  return ethers.isAddress(address);
}

/**
 * Converts an EVM address to checksum format (EIP-55)
 * This function handles both uppercase and lowercase addresses
 */
export function toChecksumAddress(address: string): string {
  if (!address || typeof address !== 'string') {
    throw new Error('Address must be a non-empty string');
  }

  const cleanAddress = address.trim();
  if (!cleanAddress.startsWith('0x')) {
    throw new Error('Address must start with 0x');
  }

  if (!validateEvmAddress(cleanAddress)) {
    throw new Error(`Invalid EVM address: ${cleanAddress}`);
  }

  return ethers.getAddress(cleanAddress);
}

/**
 * Checks if an address is already in valid checksum format (EIP-55)
 */
export function isChecksumAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false;
  }

  try {
    const cleanAddress = address.trim();
    if (!validateEvmAddress(cleanAddress)) {
      return false;
    }

    // Compare with the checksummed version
    return cleanAddress === ethers.getAddress(cleanAddress);
  } catch {
    return false;
  }
}

/**
 * Converts multiple addresses to checksum format
 */
export function toChecksumAddresses(addresses: string[]): string[] {
  return addresses.map((address) => toChecksumAddress(address));
}

/**
 * Safely converts an address to checksum format, returns null if invalid
 */
export function safeToChecksumAddress(address: string): string | null {
  try {
    return toChecksumAddress(address);
  } catch {
    return null;
  }
}

/**
 * Formats token amounts with proper decimals
 */
export function formatTokenAmount(
  amount: bigint,
  decimals: number = 18,
): string {
  return ethers.formatUnits(amount, decimals);
}

/**
 * Parses token amounts from string to bigint
 */
export function parseTokenAmount(
  amount: string,
  decimals: number = 18,
): bigint {
  return ethers.parseUnits(amount, decimals);
}

/**
 * Formats HYPE (native token) amounts
 */
export function formatHypeAmount(amount: bigint): string {
  return ethers.formatEther(amount);
}

/**
 * Parses HYPE (native token) amounts
 */
export function parseHypeAmount(amount: string): bigint {
  return ethers.parseEther(amount);
}

/**
 * Formats launchpad token amounts (6 decimals)
 */
export function formatLaunchpadTokenAmount(amount: bigint): string {
  return ethers.formatUnits(amount, 6);
}

/**
 * Parses launchpad token amounts (6 decimals)
 */
export function parseLaunchpadTokenAmount(amount: string): bigint {
  return ethers.parseUnits(amount, 6);
}

/**
 * Validates if a string is a valid transaction hash
 */
export function validateTxHash(hash: string): boolean {
  return /^0x[a-fA-F0-9]{64}$/.test(hash);
}

/**
 * Formats a transaction hash to lowercase
 */
export function formatTxHash(hash: string): string {
  if (!validateTxHash(hash)) {
    throw new Error(`Invalid transaction hash: ${hash}`);
  }
  return hash.toLowerCase();
}

/**
 * Validates if a string is a valid block number
 */
export function validateBlockNumber(blockNumber: string | number): boolean {
  const num =
    typeof blockNumber === 'string' ? parseInt(blockNumber, 10) : blockNumber;
  return Number.isInteger(num) && num >= 0;
}

/**
 * Converts various input types to a valid block number
 */
export function toBlockNumber(blockNumber: string | number | bigint): number {
  if (typeof blockNumber === 'bigint') {
    return Number(blockNumber);
  }
  if (typeof blockNumber === 'string') {
    const num = parseInt(blockNumber, 10);
    if (!validateBlockNumber(num)) {
      throw new Error(`Invalid block number: ${blockNumber}`);
    }
    return num;
  }
  if (!validateBlockNumber(blockNumber)) {
    throw new Error(`Invalid block number: ${blockNumber}`);
  }
  return blockNumber;
}

/**
 * Converts Wei to Gwei (for gas calculations)
 */
export function weiToGwei(wei: bigint): string {
  return ethers.formatUnits(wei, 'gwei');
}

/**
 * Converts Gwei to Wei (for gas calculations)
 */
export function gweiToWei(gwei: string): bigint {
  return ethers.parseUnits(gwei, 'gwei');
}

/**
 * Checks if an address is the zero address
 */
export function isZeroAddress(address: string): boolean {
  return address === ethers.ZeroAddress;
}

/**
 * Gets the zero address constant
 */
export function getZeroAddress(): string {
  return ethers.ZeroAddress;
}

/**
 * Converts a hex string to bytes
 */
export function hexToBytes(hex: string): Uint8Array {
  return ethers.getBytes(hex);
}

/**
 * Converts bytes to hex string
 */
export function bytesToHex(bytes: Uint8Array): string {
  return ethers.hexlify(bytes);
}

/**
 * Keccak256 hash function
 */
export function keccak256(data: string | Uint8Array): string {
  return ethers.keccak256(data);
}

/**
 * Encodes function call data
 */
export function encodeFunctionData(fragment: string, values: any[]): string {
  const iface = new ethers.Interface([fragment]);
  const functionName = fragment.split('(')[0].split(' ').pop();
  return iface.encodeFunctionData(functionName!, values);
}

/**
 * Decodes function call data
 */
export function decodeFunctionData(fragment: string, data: string): any[] {
  const iface = new ethers.Interface([fragment]);
  const functionName = fragment.split('(')[0].split(' ').pop();
  return iface.decodeFunctionData(functionName!, data);
}

/**
 * Sleep utility function
 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Retry utility function for async operations
 */
export async function retryAsync<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (i < maxRetries) {
        await sleep(delay * Math.pow(2, i)); // Exponential backoff
      }
    }
  }

  throw lastError!;
}
